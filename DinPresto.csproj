<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net9.0-windows10.0.19041.0</TargetFramework>
        <TargetPlatformMinVersion>10.0.17763.0</TargetPlatformMinVersion>
        <OutputType>WinExe</OutputType>

        <!-- WinUI 3 -->
        <UseWinUI>true</UseWinUI>
        <EnableMsixTooling>true</EnableMsixTooling>
        <WindowsPackageType>None</WindowsPackageType>

        <!-- Project Options -->
        <Nullable>enable</Nullable>
        <LangVersion>latest</LangVersion>
        <ImplicitUsings>enable</ImplicitUsings>
        <RootNamespace>DinPresto</RootNamespace>

        <!-- App Options -->
        <UseRidGraph>true</UseRidGraph>
        <Platforms>x86;x64;ARM64</Platforms>
        <ApplicationManifest>app.manifest</ApplicationManifest>
        <PublishProfile>win-$(Platform).pubxml</PublishProfile>
        <RuntimeIdentifiers Condition="$([MSBuild]::GetTargetFrameworkVersion('$(TargetFramework)')) &gt;= 8">win-x86;win-x64;win-arm64</RuntimeIdentifiers>
        <RuntimeIdentifiers Condition="$([MSBuild]::GetTargetFrameworkVersion('$(TargetFramework)')) &lt; 8">win10-x86;win10-x64;win10-arm64</RuntimeIdentifiers>
    </PropertyGroup>

    <ItemGroup>
        <Content Include="Assets\SplashScreen.scale-200.png" />
        <Content Include="Assets\LockScreenLogo.scale-200.png" />
        <Content Include="Assets\Square150x150Logo.scale-200.png" />
        <Content Include="Assets\Square44x44Logo.scale-200.png" />
        <Content Include="Assets\Square44x44Logo.targetsize-24_altform-unplated.png" />
        <Content Include="Assets\StoreLogo.png" />
        <Content Include="Assets\Wide310x150Logo.scale-200.png" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.9" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.9">
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
          <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.9" />
        <PackageReference Include="Microsoft.WindowsAppSDK" Version="1.*" />
        <PackageReference Include="Microsoft.Web.WebView2" Version="1.*" />
        <PackageReference Include="Microsoft.Windows.SDK.BuildTools" Version="10.*" />

        <Manifest Include="$(ApplicationManifest)" />
    </ItemGroup>

    <!-- 
        Defining the "Msix" ProjectCapability here allows the Single-project MSIX Packaging
        Tools extension to be activated for this project even if the Windows App SDK Nuget
        package has not yet been restored.
    -->
    <ItemGroup Condition="'$(DisableMsixProjectCapabilityAddedByProject)'!='true' and '$(EnableMsixTooling)'=='true'">
        <ProjectCapability Include="Msix" />
    </ItemGroup>

    <!-- 
        Defining the "HasPackageAndPublishMenuAddedByProject" property here allows the Solution 
        Explorer "Package and Publish" context menu entry to be enabled for this project even if 
        the Windows App SDK Nuget package has not yet been restored.
    -->
    <PropertyGroup Condition="'$(DisableHasPackageAndPublishMenuAddedByProject)'!='true' and '$(EnableMsixTooling)'=='true'">
        <HasPackageAndPublishMenu>true</HasPackageAndPublishMenu>
    </PropertyGroup>
</Project>
