using Microsoft.UI.Xaml.Navigation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using DinPresto.Data;
using DinPresto.Services;
using DinPresto.ViewModels;
using DinPresto.Views;

namespace DinPresto
{
    /// <summary>
    /// Provides application-specific behavior to supplement the default Application class.
    /// </summary>
    public partial class App : Application
    {
        private Window? window;
        private IHost? host;

        /// <summary>
        /// Initializes the singleton application object.  This is the first line of authored code
        /// executed, and as such is the logical equivalent of main() or WinMain().
        /// </summary>
        public App()
        {
            this.InitializeComponent();

            // Configurar servicios
            host = Host.CreateDefaultBuilder()
                .ConfigureServices((context, services) =>
                {
                    // Configurar Entity Framework
                    services.AddDbContext<DinPrestoContext>(options =>
                        options.UseSqlite("Data Source=dinpresto.db"));

                    // Registrar servicios
                    services.AddScoped<IAmortizacionService, AmortizacionService>();

                    // Registrar ViewModels
                    services.AddTransient<MainViewModel>();
                })
                .Build();
        }

        /// <summary>
        /// Invoked when the application is launched normally by the end user.  Other entry points
        /// will be used such as when the application is launched to open a specific file.
        /// </summary>
        /// <param name="e">Details about the launch request and process.</param>
        protected override void OnLaunched(LaunchActivatedEventArgs e)
        {
            window = new Window();
            window.Title = "DinPresto - Sistema de Gestión de Préstamos";

            if (window.Content is not Frame rootFrame)
            {
                rootFrame = new Frame();
                rootFrame.NavigationFailed += OnNavigationFailed;
                window.Content = rootFrame;
            }

            // Inicializar la base de datos
            InitializeDatabaseAsync();

            _ = rootFrame.Navigate(typeof(MainPage), e.Arguments);
            window.Activate();
        }

        private async void InitializeDatabaseAsync()
        {
            try
            {
                using var scope = host?.Services.CreateScope();
                var context = scope?.ServiceProvider.GetRequiredService<DinPrestoContext>();
                if (context != null)
                {
                    await context.Database.EnsureCreatedAsync();
                }
            }
            catch (Exception ex)
            {
                // Log error - en una implementación real usaríamos un logger
                System.Diagnostics.Debug.WriteLine($"Error inicializando base de datos: {ex.Message}");
            }
        }

        /// <summary>
        /// Invoked when Navigation to a certain page fails
        /// </summary>
        /// <param name="sender">The Frame which failed navigation</param>
        /// <param name="e">Details about the navigation failure</param>
        void OnNavigationFailed(object sender, NavigationFailedEventArgs e)
        {
            throw new Exception("Failed to load Page " + e.SourcePageType.FullName);
        }

        protected override void OnClosed(WindowEventArgs args)
        {
            host?.Dispose();
            base.OnClosed(args);
        }
    }
}
