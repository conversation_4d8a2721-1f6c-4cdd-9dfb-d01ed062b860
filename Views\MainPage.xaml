<?xml version="1.0" encoding="UTF-8" ?>
<Page
    x:Class="DinPresto.Views.MainPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="using:DinPresto"
    xmlns:viewmodels="using:DinPresto.ViewModels"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{ThemeResource SystemAccentColor}" Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <FontIcon Glyph="&#xE8AB;" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                    <TextBlock Text="DinPresto" FontSize="24" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                    <TextBlock Text="Sistema de Gestión de Préstamos" FontSize="14" Foreground="White"
                              Opacity="0.8" VerticalAlignment="Center" Margin="20,0,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="Actualizar" Margin="0,0,10,0"/>
                    <Ellipse Width="32" Height="32" Fill="{ThemeResource SystemAccentColor}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Navigation Menu -->
            <Border Grid.Column="0" Background="{ThemeResource LayerFillColorDefaultBrush}"
                   BorderBrush="{ThemeResource CardStrokeColorDefaultBrush}" BorderThickness="0,0,1,0">
                <ScrollViewer>
                    <StackPanel Margin="10">
                        <TextBlock Text="NAVEGACIÓN" FontSize="12" FontWeight="SemiBold"
                                  Foreground="{ThemeResource TextFillColorSecondaryBrush}" Margin="10,10,10,5"/>

                        <Button Content="📊 Dashboard" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left"
                               Margin="0,2" Padding="10,8"/>

                        <Button Content="👥 Clientes" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left"
                               Margin="0,2" Padding="10,8"/>

                        <Button Content="💰 Préstamos" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left"
                               Margin="0,2" Padding="10,8"/>

                        <Button Content="💳 Pagos" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left"
                               Margin="0,2" Padding="10,8"/>

                        <Button Content="📄 Reportes" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left"
                               Margin="0,2" Padding="10,8"/>

                        <Rectangle Height="1" Fill="Gray" Margin="0,10"/>

                        <Button Content="⚙️ Configuración" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left"
                               Margin="0,2" Padding="10,8"/>
                    </StackPanel>
                </ScrollViewer>
            </Border>

            <!-- Content Area -->
            <ScrollViewer Grid.Column="1" Padding="20">
                <StackPanel Spacing="20">
                    <!-- Dashboard Title -->
                    <TextBlock Text="Dashboard" FontSize="28" FontWeight="Bold"/>

                    <!-- Dashboard Cards -->
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Card 1: Total Clientes -->
                        <Border Grid.Column="0" Grid.Row="0" Background="{ThemeResource CardBackgroundFillColorDefaultBrush}"
                               CornerRadius="8" Padding="20" Margin="0,0,10,10">
                            <StackPanel>
                                <Grid>
                                    <FontIcon Glyph="&#xE716;" FontSize="24" Foreground="{ThemeResource SystemAccentColor}" HorizontalAlignment="Left"/>
                                    <TextBlock Text="125" FontSize="32" FontWeight="Bold" HorizontalAlignment="Right"/>
                                </Grid>
                                <TextBlock Text="Total Clientes" FontSize="14" Foreground="{ThemeResource TextFillColorSecondaryBrush}" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Border>

                        <!-- Card 2: Total Préstamos -->
                        <Border Grid.Column="1" Grid.Row="0" Background="{ThemeResource CardBackgroundFillColorDefaultBrush}"
                               CornerRadius="8" Padding="20" Margin="0,0,10,10">
                            <StackPanel>
                                <Grid>
                                    <FontIcon Glyph="&#xE8AB;" FontSize="24" Foreground="#FF6B46C1" HorizontalAlignment="Left"/>
                                    <TextBlock Text="89" FontSize="32" FontWeight="Bold" HorizontalAlignment="Right"/>
                                </Grid>
                                <TextBlock Text="Total Préstamos" FontSize="14" Foreground="{ThemeResource TextFillColorSecondaryBrush}" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Border>

                        <!-- Card 3: Préstamos Activos -->
                        <Border Grid.Column="2" Grid.Row="0" Background="{ThemeResource CardBackgroundFillColorDefaultBrush}"
                               CornerRadius="8" Padding="20" Margin="0,0,10,10">
                            <StackPanel>
                                <Grid>
                                    <FontIcon Glyph="&#xE73E;" FontSize="24" Foreground="#FF10B981" HorizontalAlignment="Left"/>
                                    <TextBlock Text="67" FontSize="32" FontWeight="Bold" HorizontalAlignment="Right"/>
                                </Grid>
                                <TextBlock Text="Préstamos Activos" FontSize="14" Foreground="{ThemeResource TextFillColorSecondaryBrush}" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Border>
                    </Grid>
                </StackPanel>
            </ScrollViewer>
        </Grid>
    </Grid>
</Page>
